# tools.py

def addTask(task: str, tags: list[str] = None, specific_time: str = None, time_frame: str = None):
    """
    用于从用户的自然语言中提取信息并创建一个新任务。
    
    Args:
        task (str): 任务的核心内容。例如'交电费', '把设计稿发给小王'。
        tags (list[str]): 任务的分类标签，例如 '工作', '家务', '紧急'。
        specific_time (str): 具体的日期和时间，格式为 ISO 8601。
        time_frame (str): 一个模糊的时间描述，例如 '明天下午', '这周末'。
    """
    print("--- [工具执行]: addTask ---")
    return {"status": "success", "action": "addTask", "parameters": locals()}

def planProject(project_name: str):
    """
    当用户想要规划一个复杂项目或任务时调用。
    
    Args:
        project_name (str): 用户想要规划的项目名称。
    """
    print("--- [工具执行]: planProject ---")
    # 注意：在MVP 0.5阶段，我们只识别意图。后续版本将在这里启动一个交互式循环。
    return {"status": "success", "action": "planProject", "parameters": locals()}

def queryTasks(tags: list[str] = None, time_frame: str = None, keyword: str = None):
    """
    当用户想要查询或筛选任务时调用。
    
    Args:
        tags (list[str]): 用于筛选的标签。
        time_frame (str): 用于筛选的时间范围。
        keyword (str): 用于筛选的关键词。
    """
    print("--- [工具执行]: queryTasks ---")
    # 注意：在MVP 0.5阶段，我们只打印筛选条件，因为没有数据库。
    return {"status": "success", "action": "queryTasks", "parameters": locals()}

def updateTask(target_task: str, updates: dict):
    """
    当用户想要修改一个已存在的任务时调用。
    
    Args:
        target_task (str): 对要修改的目标任务的描述，用于定位任务。
        updates (dict): 一个包含要更新字段和新值的字典。例如 {'tags': ['紧急'], 'time_frame': '周四下午'}。
    """
    print("--- [工具执行]: updateTask ---")
    return {"status": "success", "action": "updateTask", "parameters": locals()}

def completeTask(target_task: str):
    """
    当用户表示一个任务已经完成时调用。
    
    Args:
        target_task (str): 对已完成任务的描述。
    """
    print("--- [工具执行]: completeTask ---")
    return {"status": "success", "action": "completeTask", "parameters": locals()}

def deleteTask(target_task: str):
    """
    当用户想要取消或删除一个任务时调用。
    
    Args:
        target_task (str): 对要删除任务的描述。
    """
    print("--- [工具执行]: deleteTask ---")
    return {"status": "success", "action": "deleteTask", "parameters": locals()}

def getSuggestion(energy_level: str = None, time_available_minutes: int = None):
    """
    当用户寻求建议，不知道该做什么时调用。
    
    Args:
        energy_level (str): 用户描述的当前精力状态，例如 "很累", "精力充沛"。
        time_available_minutes (int): 用户可用的时间，以分钟为单位。
    """
    print("--- [工具执行]: getSuggestion ---")
    return {"status": "success", "action": "getSuggestion", "parameters": locals()}