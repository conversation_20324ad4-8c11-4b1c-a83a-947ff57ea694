# tool_declarations.py

from google.genai import types
import tools

# --- 1. addTask 的智能声明 ---
add_task_decl = types.FunctionDeclaration(
    name='addTask',
    description=tools.addTask.__doc__,
    parameters=types.Schema(
        type='OBJECT',
        properties={
            'task': types.Schema(type='STRING', description="任务的核心内容。"),
            'tags': types.Schema(
                type='ARRAY', 
                items=types.Schema(type='STRING'),
                description="任务的分类标签。请根据任务内容进行推理，如果没有明确的标签，可以尝试生成1-2个相关的标签，例如'学习', '工作', '家务', '锻炼'等。"
            ),
            'specific_time': types.Schema(type='STRING', description="具体的日期和时间，格式为 ISO 8601。请根据上下文和当前时间进行转换。"),
            'time_frame': types.Schema(type='STRING', description="一个模糊的时间描述，例如 '明天下午'。")
        },
        required=['task']
    )
)

# --- 2. planProject 的智能声明 ---
plan_project_decl = types.FunctionDeclaration(
    name='planProject',
    description=tools.planProject.__doc__,
    parameters=types.Schema(
        type='OBJECT',
        properties={
            'project_name': types.Schema(type='STRING', description="用户想要规划的复杂项目的名称。")
        },
        required=['project_name']
    )
)

# --- 3. queryTasks 的智能声明 ---
query_tasks_decl = types.FunctionDeclaration(
    name='queryTasks',
    description=tools.queryTasks.__doc__,
    parameters=types.Schema(
        type='OBJECT',
        properties={
            'tags': types.Schema(
                type='ARRAY',
                items=types.Schema(type='STRING'),
                description="用于筛选的标签。"
            ),
            'time_frame': types.Schema(
                type='STRING',
                description="用于筛选的时间范围。请将口语化的时间，如'今天'、'这周末'，转换为更精确的描述。"
            ),
            'keyword': types.Schema(
                type='STRING',
                description="用于在任务内容中搜索的关键词。"
            )
        }
    )
)

# --- 4. updateTask 的智能声明 ---
update_task_decl = types.FunctionDeclaration(
    name='updateTask',
    description=tools.updateTask.__doc__,
    parameters=types.Schema(
        type='OBJECT',
        properties={
            'target_task': types.Schema(type='STRING', description="对要修改的目标任务的清晰描述，用于定位任务。"),
            'updates': types.Schema(
                type='OBJECT',
                description="一个包含要更新字段和新值的字典。请将用户的口语化修改（如‘推迟一天’、‘提前2小时’）转换为结构化的更新内容。例如: {'time_frame': '明天', 'tags': ['紧急']}。"
            )
        },
        required=['target_task', 'updates']
    )
)

# --- 5 & 6. completeTask 和 deleteTask 的智能声明 ---
# 这两个比较类似，可以共用一些逻辑
task_identity_param = types.Schema(
    type='OBJECT',
    properties={
        'target_task': types.Schema(type='STRING', description="对目标任务的清晰、唯一的描述。请综合上下文，找到用户最可能指向的那个任务的描述。")
    },
    required=['target_task']
)
complete_task_decl = types.FunctionDeclaration(name='completeTask', description=tools.completeTask.__doc__, parameters=task_identity_param)
delete_task_decl = types.FunctionDeclaration(name='deleteTask', description=tools.deleteTask.__doc__, parameters=task_identity_param)

# --- 7. getSuggestion 的智能声明 ---
get_suggestion_decl = types.FunctionDeclaration(
    name='getSuggestion',
    description=tools.getSuggestion.__doc__,
    parameters=types.Schema(
        type='OBJECT',
        properties={
            'energy_level': types.Schema(
                type='STRING',
                description="用户的精力状态。请将用户的模糊描述（如‘有点乏’、‘没啥精神’、‘状态很好’）归一化为‘高’、‘中’、‘低’中的一种。"
            ),
            'time_available_minutes': types.Schema(
                type='INTEGER',
                description="用户可用的时间，以分钟为单位。请从'一小时'、'半小时'等描述中提取出数字。"
            )
        }
    )
)

# --- 将所有工具打包，以便其他文件导入 ---
ALL_TOOLS = [
    types.Tool(function_declarations=[add_task_decl]),
    types.Tool(function_declarations=[plan_project_decl]),
    types.Tool(function_declarations=[query_tasks_decl]),
    types.Tool(function_declarations=[update_task_decl]),
    types.Tool(function_declarations=[complete_task_decl]),
    types.Tool(function_declarations=[delete_task_decl]),
    types.Tool(function_declarations=[get_suggestion_decl]),
]