# main.py

from dotenv import load_dotenv
from task_parser import TaskParser

def main():
    """
    程序主函数
    """
    # 加载 .env 文件中的环境变量
    load_dotenv()
    
    # 初始化任务解析器
    parser = TaskParser()
    
    # --- 使用您需求文档中的示例进行批量测试 ---
    example_prompts = [
        # addTask
        "提醒我明天要交电费",
        "我今天5点希望学一小时MCP",
        # planProject
        "我需要准备下个月的技术分享会",
        # queryTasks
        "我今天有什么事？",
        "给我看看所有带'工作'标签的任务",
        # updateTask
        "把'周三开会'改到周四下午",
        # completeTask
        "'回复John的邮件'这件事我做完了",
        # deleteTask
        "取消'买咖啡豆'这个任务",
        # getSuggestion
        "我现在有点累，有什么推荐做的吗？",
        # chitChat
        "今天天气真不错"
    ]
    
    for prompt in example_prompts:
        parser.parse(prompt)

    # --- 开启交互模式 ---
    print("\n==============================")
    print("测试完成。进入交互模式。")
    print("输入 'quit' 或 'exit' 退出。")
    print("==============================")
    while True:
        user_input = input("You: ")
        if user_input.lower() in ["quit", "exit"]:
            print("再见！")
            break
        parser.parse(user_input)

if __name__ == "__main__":
    main()