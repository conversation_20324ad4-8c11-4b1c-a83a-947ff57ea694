# MVP 0.5 技术需求规格文档 (Spec)

**项目:** 心流 (FlowState)  
**阶段:** MVP 0.5 - AI核心逻辑验证  
**关联主文档:** 项目需求文档 (PRD) v2.0

## 1. 阶段概述

### 1.1 核心目标
本阶段的唯一目标是：验证通过大语言模型（LLM）API，将用户的自然语言输入稳定、可靠地转化为结构化数据的核心技术通路。我们需要攻克项目最大的技术风险，即与AI的交互。

### 1.2 最终交付物
一个可在本地命令行环境运行的Python程序。该程序需具备以下能力：

- 接收用户在命令行输入的单句或多句对话。
- 调用LLM API进行意图识别和信息提取。
- 在命令行打印出解析后的、结构化的JSON数据或相应的对话回复。

### 1.3 明确排除项
- **无图形用户界面 (GUI):** 所有交互均在命令行（终端）完成。
- **无数据库:** 所有任务数据均为临时处理，不进行持久化存储。
- **无后端服务器:** 程序为本地脚本，不提供Web API接口。

## 2. AI意图处理逻辑详解

程序的核心是围绕一个主控逻辑构建：接收用户输入 -> 调用AI进行意图识别 -> 根据不同意图执行相应操作。

### 2.1 意图: addTask (添加简单任务)

**用户需求:** 快速记录、添加详细任务。

**触发语示例:** 提醒我明天要交电费, 这周五下午5点去健身, 把设计稿发给小王

**核心处理逻辑:**
- AI识别意图为 addTask。
- AI从输入中提取任务的关键信息：task (任务内容), tags (标签，可选), specific_time (具体时间，可选), time_frame (模糊时间，可选)。
- 特别注意: 指示AI尽可能将相对时间（如"明天下午5点"）转换为绝对时间戳（ISO 8601格式），需在Prompt中提供当前日期作为参考。
- 程序打印出包含这些详细信息的JSON。

### 2.2 意图: planProject (规划复杂项目)

**用户需求:** 引导式地拆解一个大任务。

**触发语示例:** 我需要准备下个月的技术分享会, 帮我规划一下"欧洲旅行"

**核心处理逻辑:**
- AI首次识别意图为 planProject，并提取项目名称。
- 程序不直接创建任务，而是进入一个**"规划循环"**。
- 程序根据AI返回的引导性问题（如response字段），向用户发起追问。
- 用户回答后，程序将用户的回答与项目上下文一起，再次发送给AI，请求下一步的引导或总结。
- 循环持续，直到用户确认规划完成。
- 最后，程序将拆解出的所有子任务，以addTask的格式逐一打印出来。

### 2.3 意图: queryTasks (查询任务)

**用户需求:** 按条件筛选或确认任务。

**触发语示例:** 我今天有什么事？, 给我看看所有带"工作"标签的任务, 我有没有记下"买机票"？

**核心处理逻辑:**
- AI识别意图为 queryTasks。
- AI从输入中提取筛选条件：tags, time_frame, keyword等。
- 程序打印出包含这些filters的JSON。
- 注意: 在MVP 0.5阶段，我们没有数据库，所以程序只需打印出筛选条件即可。在后续版本中，后端将根据这些条件去查询数据库。

### 2.4 意图: updateTask (修改任务)

**用户需求:** 调整任务细节。

**触发语示例:** 把"周三开会"改到周四下午, 给"买牛奶"那个任务加上"紧急"标签

**核心处理逻辑:**
- AI识别意图为 updateTask。
- AI需要识别出两部分信息：target_task (要修改的那个任务的描述) 和 updates (具体要更新的内容)。
- 程序打印出包含这两部分信息的JSON。

### 2.5 意图: completeTask / deleteTask (管理任务)

**用户需求:** 完成或取消任务。

**触发语示例:** "回复John的邮件"这件事我做完了, 取消"买咖啡豆"这个任务

**核心处理逻辑:**
- AI识别意图为 completeTask 或 deleteTask。
- AI提取出 target_task (目标任务的描述)。
- 程序打印出包含意图和目标任务的JSON。

### 2.6 意图: getSuggestion (寻求建议)

**用户需求:** 在选择困难时获得智能推荐。

**触发语示例:** 我现在有点累，有什么推荐做的吗？, 帮我挑一件15分钟内能搞定的事

**核心处理逻辑:**
- AI识别意图为 getSuggestion。
- AI从输入中提取用户的上下文信息，如 energy_level (精力状态), time_available (可用时长)等。
- 程序打印出包含这些context的JSON。

### 2.7 意图: chitChat (自然闲聊)

**用户需求:** 进行非任务性的自然对话。

**触发语示例:** 今天天气真不错, 晚安

**核心处理逻辑:**
- AI识别意图为 chitChat。
- AI生成一句符合对话场景的回复。
- 程序直接将AI生成的response文本打印给用户，实现对话。

## 3. 关键技术考量

- **Prompt工程:** 这是本阶段的重中之重。需要设计一个包含角色扮演、任务描述、输出格式约束和少量示例的"主Prompt"。

- **Function Calling / Tool Use:** 强烈建议研究并使用LLM提供的此功能。通过在代码中定义工具（如addTask, queryTasks等），让AI返回调用哪个工具以及相应的参数，可以极大地提升输出结果的稳定性和准确性。

- **代码封装:** 所有与AI交互的逻辑都应封装在一个独立的Python类中（例如 TaskParser），以实现高内聚、低耦合，方便未来被后端服务调用。

## 4. 成功标准

- **功能性:** 程序能够稳定运行，对于PRD中定义的80%以上的典型用户输入，能返回格式正确、意图识别准确、内容合理的JSON。

- **可靠性:** 在使用Function Calling/Tool Use时，返回结果的结构准确率应接近100%。

- **代码质量:** TaskParser模块逻辑清晰，易于理解和维护。
