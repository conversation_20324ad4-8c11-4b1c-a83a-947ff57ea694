# 项目需求文档 (PRD): Efflo

**版本：** 1.0  
**创建日期：** 2025年7月10日  
**项目经理：** 陈启龙  
**状态：** 已根据"AI核心优先"策略重构

## 1. 项目概述 (Project Overview)

### 1.1 项目背景与问题陈述
传统的待办事项（To-Do List）和日历工具通常要求用户进行精确到分钟的时间规划。这种僵化的模式对于思维方式更发散、工作流更具创造性的用户（常被称为"P人"或"感知型"人格）来说，不仅难以维持，还常常因为计划被打乱而产生焦虑感和挫败感。当一个任务延期，往往会引发连锁反应，导致整个计划的重构，我们称之为"计划的脆弱性"。

### 1.2 产品愿景 (Product Vision)
我们致力于打造一个智能的、适应性的规划伙伴。它能够通过自然语言对话，理解用户的抽象意图、精力状态和情绪背景，将僵硬的时间表转化为灵活的"任务流"。我们的愿景是：让规划成为释放压力、激发动力的工具，而不是创造压力的来源。

### 1.3 目标用户画像 (Target Audience Persona)
**姓名：** Alex

**身份：** 创意工作者（设计师、作家、策略师）或需要高度自主性的学生/自由职业者。

**特点：**
- **P人特质：** 喜欢保持选择的开放性，讨厌被严格的日程束缚。
- **工作模式：** 依赖灵感和心流（Flow State），工作效率与当前精力状态高度相关。
- **痛点：** 尝试过多种待办事项应用，但都因维护成本太高、计划赶不上变化而放弃。经常感到"想做的事很多，但不知从何下手"。
- **期望：** 希望有一个"聪明的助理"，能帮他/她整理思绪，并在"对的时间"提醒他/她做"对的事"。

### 1.4 核心价值主张 (Core Value Proposition)
**心流 (FlowState)：** 让你的计划适应你，而不是你适应计划。我们通过对话式AI，将你的想法无压力地转化为可执行的、与你当前状态匹配的任务建议。

## 2. MVP范围与开发策略

### 2.1 开发策略：AI核心优先 (AI-Core First)
我们将采用"风险驱动"的开发策略。首先集中攻克项目最大的技术不确定性——即与大语言模型(LLM)的交互，确保其能稳定、可靠地工作。在此基础上，再进行应用层面的封装。

### 2.2 第一阶段: MVP 0.5 - AI核心逻辑验证
**阶段目标:** 验证通过LLM API将自然语言稳定转化为结构化任务数据的核心技术通路。产出一个可在本地运行、可交互的AI解析器。

**产出形态:** 一个本地运行的Python脚本/模块，提供命令行交互界面。

**核心功能:**
- **F0.1:** 成功调用LLM API: 编写代码，成功向LLM服务商发送请求并获得返回。
- **F0.2:** 精准的Prompt工程: 通过精心设计和反复测试Prompt（包含指令、示例、格式要求），使LLM能够稳定地将输入的自然语言（如"下周末找个下午把书房整理一下"）解析为我们预设的JSON结构（如{"task": "整理书房", "tags": ["家务", "需专注"], "time_frame": "下周末下午"}）。
- **F0.3:** 核心逻辑封装: 将API调用、Prompt处理、结果解析等逻辑封装成一个可复用的Python类或模块，方便未来被后端API调用。

### 2.3 第二阶段: MVP 1.0 - 全栈应用封装
**阶段目标:** 将已经验证的AI核心逻辑，通过前后端技术封装成一个用户可用的、具备数据持久化能力的Web应用。

**产出形态:** 一个包含前端、后端和数据库的全栈Web应用。

**核心功能:**
- **F1.1:** 后端API封装: 建立一个FastAPI后端服务，创建API接口（如/api/tasks），该接口接收前端传来的用户输入，并调用MVP 0.5中完成的AI核心模块进行处理。
- **F1.2:** 数据持久化: 将AI模块解析出的结构化JSON数据，存入MongoDB数据库中。
- **F1.3:** 前端用户界面: 构建一个简洁的前端页面，让用户可以：
  - 输入任务（聊天框）
  - 查看由后端返回、AI解析后的任务列表
  - 对任务进行基础管理（如删除）
  - 通过点击标签进行多维度查询

## 3. 技术栈概要 (Technical Overview)
- **MVP 0.5:** Python, 第三方LLM API库 (如 openai, google-generativeai)
- **MVP 1.0:**
  - **前端:** HTML, Tailwind CSS, Vanilla JavaScript (或轻量级框架如Vue/React)
  - **后端:** Python (FastAPI)
  - **数据库:** MongoDB
  - **核心AI:** MVP 0.5中完成的Python模块

## 4. 成功衡量指标 (Success Metrics)
### MVP 0.5:
- **功能性指标:** AI核心模块能够稳定运行，对于80%以上的典型用户输入，能返回格式正确、内容合理的JSON。
- **非功能性指标:** 代码封装良好，易于被未来的后端服务调用。

### MVP 1.0:
- **核心功能可用性:** 所有F1.1至F1.3的功能均能稳定运行，数据在前后端正确同步。
- **用户体验指标:**
  - **任务创建成功率:** 用户提交的自然语言任务，成功被解析并显示在列表中的比例 > 95%。
  - **定性反馈:** 通过你自己的使用，评估端到端的体验是否流畅、无压力。
- **开发周期:** 是否在8月底前完成核心功能。

## 5. 风险与挑战 (Risks & Challenges)
- **技术风险:** LLM API的响应延迟或解析不准确，可能影响核心用户体验。需要设计好加载状态和错误处理机制。
