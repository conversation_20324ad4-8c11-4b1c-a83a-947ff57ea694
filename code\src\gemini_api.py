import os
import json
import google.genai as genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()

# --------------------------------------------------------------------------
# 步骤 1: 定义我们本地的 Python 函数 (这部分不变)
# 这是我们希望 AI 在解析后，由我们的代码去调用的真实函数。
# --------------------------------------------------------------------------
def addTask(task: str, tags: list[str] = None, specific_time: str = None, time_frame: str = None):
    """
    一个用于添加任务的函数存根 (Function Stub)。
    它接收任务的详细信息并打印出来，模拟任务被创建的过程。
    """
    print("--- 准备调用本地 addTask 函数 ---")
    print(f"  任务内容 (task): {task}")
    print(f"  标签 (tags): {tags if tags else '无'}")
    print(f"  具体时间 (specific_time): {specific_time if specific_time else '无'}")
    print(f"  模糊时间 (time_frame): {time_frame if time_frame else '无'}")
    
    return {
        "function": "addTask",
        "parameters": {
            "task": task,
            "tags": tags,
            "specific_time": specific_time,
            "time_frame": time_frame
        }
    }

# --------------------------------------------------------------------------
# 步骤 2: 定义我们的 TaskParser 类 (遵照 README.md 进行重构)
# --------------------------------------------------------------------------
class TaskParser:
    def __init__(self, model_name="gemini-2.5-flash"):
        self.model_name = model_name
        
        # 按照文档，初始化客户端会自动读取环境变量
        try:
            self.client = genai.Client()
        except Exception:
            print("错误：客户端初始化失败。请确保 GOOGLE_API_KEY 环境变量已正确设置。")
            exit()

        # 按照文档，手动声明函数和工具
        add_task_func_declaration = types.FunctionDeclaration(
            name='addTask',
            description='用于从用户的自然语言中提取信息并创建一个新任务。',
            parameters=types.Schema(
                type='OBJECT',
                properties={
                    'task': types.Schema(type='STRING', description="任务的核心内容。例如'交电费'。"),
                    'tags': types.Schema(
                        type='ARRAY', 
                        items=types.Schema(type='STRING'),
                        description="任务的分类标签，例如 '工作', '家务'。"
                    ),
                    'specific_time': types.Schema(type='STRING', description="具体的日期和时间，格式为 ISO 8601。"),
                    'time_frame': types.Schema(type='STRING', description="一个模糊的时间描述，例如 '明天下午'。")
                },
                required=['task']
            )
        )
        # 将函数声明包装成一个工具
        self.tool = types.Tool(function_declarations=[add_task_func_declaration])

    def parse(self, user_input: str):
        """
        接收用户输入，调用 generate_content API，并解析返回的工具调用请求。
        """
        print(f"\n==============================\n用户输入: '{user_input}'")
        print("正在调用 Gemini API 进行解析...")

        try:
            # 按照文档，使用 client.models.generate_content
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=user_input,
                config=types.GenerateContentConfig(tools=[self.tool])
            )
            
            # 按照文档，检查 response.function_calls
            if response.function_calls:
                function_call = response.function_calls[0]
                function_name = function_call.name
                
                if function_name == 'addTask':
                    function_args = dict(function_call.args)
                    # 在本地实际执行该函数
                    function_response = addTask(**function_args)
                    
                    print("--- 函数调用成功 ---")
                    print("最终输出的结构化JSON:")
                    print(json.dumps(function_response, indent=2, ensure_ascii=False))
                else:
                    print(f"错误：模型想调用一个未定义的函数 '{function_name}'")
            else:
                 print("--- 模型没有决定调用任何工具 ---")
                 print("模型回复:", response.text)

        except errors.APIError as e:
            print(f"API 调用时发生错误: {e.message}")
        except Exception as e:
            print(f"发生了未知错误: {e}")

# --------------------------------------------------------------------------
# 步骤 3: 主程序入口 (基本不变)
# --------------------------------------------------------------------------
if __name__ == "__main__":
    parser = TaskParser()
    
    example_prompts = [
        "提醒我明天要交电费",
        "这周五下午5点去健身，这件事挺紧急的",
        "把'完成季度报告'这个任务加上'工作'和'重要'两个标签",
        "你好啊" # 这个例子不会触发函数调用
    ]
    
    for prompt in example_prompts:
        parser.parse(prompt)