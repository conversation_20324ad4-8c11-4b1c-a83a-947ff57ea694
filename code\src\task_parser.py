# task_parser.py

import json
import google.genai as genai
from google.genai import types
import tools
import tool_declarations

class TaskParser:
    def __init__(self, model_name="gemini-2.0-flash"):
        self.model_name = model_name
        
        try:
            self.client = genai.Client()
        except Exception:
            print("错误：客户端初始化失败。请确保 GOOGLE_API_KEY 环境变量已正确设置。")
            exit()

        self.available_tools = {
            "addTask": tools.addTask, "planProject": tools.planProject, "queryTasks": tools.queryTasks,
            "updateTask": tools.updateTask, "completeTask": tools.completeTask, "deleteTask": tools.deleteTask,
            "getSuggestion": tools.getSuggestion,
        }

        self.tools = tool_declarations.ALL_TOOLS

    def parse(self, user_input: str):
        # parse 方法无需任何修改
        print(f"\n==============================\n用户输入: '{user_input}'")
        print("正在调用 Gemini API 进行解析...")
        try:
            response = self.client.models.generate_content(
                model=self.model_name, contents=user_input,
                config=types.GenerateContentConfig(tools=self.tools)
            )
            if response.function_calls:
                # ...后续逻辑不变...
                for function_call in response.function_calls:
                    function_name = function_call.name
                    function_to_call = self.available_tools.get(function_name)
                    if function_to_call:
                        function_args = dict(function_call.args)
                        function_response = function_to_call(**function_args)
                        print("--- AI决策完成，调用本地函数 ---")
                        print(json.dumps(function_response, indent=2, ensure_ascii=False))
            else:
                 print("--- 模型没有决定调用任何工具 ---")
                 print("模型回复:", response.text)
        except Exception as e:
            print(f"发生了未知错误: {e}")